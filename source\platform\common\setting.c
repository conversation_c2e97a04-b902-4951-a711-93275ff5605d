#include "setting.h"
#include <stdlib.h>  // 为malloc函数添加头文件
#ifndef WIN32
#include "xsj_lib.h"
#endif

const int g_baudRate[6] = {	1200,	4800,	9600,	14400,	19200};//!!! 485波特率

/**************************************  定值数据集相关接口   *************************************/
//返回定值值，会对定值进行缩放
int mod_getSetByDesc(char* modname, char* desc)
{
	int i;
	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if(modname)
		{
				if (!g_setDataSheet[i].name.modName)		//该定值没模块名
					continue;	
				
				if (strcmp(modname, g_setDataSheet[i].name.modName))		//模块名不对
					continue;
		}
		if (!strcmp(desc, g_setDataSheet[i].name.desc))	//比较信号名
		{
			return retSetVal(g_setDataSheet[i].val, g_setDataSheet[i].diffPoint);
		}
	}
	return 0;
}

#if 0
int mod_getSetPointByDesc(char* modname, char* desc)
{
	int i;
	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if(modname)
		{
				if (!g_setDataSheet[i].name.modName)		//该定值没模块名
					continue;	
				
				if (strcmp(modname, g_setDataSheet[i].name.modName))		//模块名不对
					continue;
		}
		if (!strcmp(desc, g_setDataSheet[i].name.desc))	//比较信号名
		{
			return g_setDataSheet[i].point;
		}
	}
	return 0;
}
#endif

//获取定值值，会缩放到 DEFAULT_POINT 对应小数点 本程序统一用扩大到小数点后3位的值，如定值是 10V 返回 10000 ,是1.11V 返回 1110, s 返回的是 ms
int getSetByDesc(char* desc)
{
	int i;
	char *modName, *sigName, *dot;
	int modNameLen;

	if (!desc)
		return 0;

	//得到字符串里 . 的位置
	dot = strchr(desc,'.');
	if (!dot)
	{	//没有 .
		sigName = desc;
		modName = 0;
	}
	else
	{	//有 .
		sigName = dot + 1;
		modName = desc;
		modNameLen = dot - modName;
	}

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (modName)		//对模块名筛选
		{
			if (!g_setDataSheet[i].name.modName)
				continue;

			if (strlen(g_setDataSheet[i].name.modName) != modNameLen)
				continue;

			if (strncmp(modName, g_setDataSheet[i].name.modName, modNameLen) != 0)
				continue;
		}
		else
		{
			if (g_setDataSheet[i].name.modName)
				continue;
		}

		if (!strcmp(sigName, g_setDataSheet[i].name.desc))	//比较信号名
		{
			return retSetVal(g_setDataSheet[i].val, g_setDataSheet[i].diffPoint);
		}
	}
	return 0;
}

//获取定值值，会缩放到 DEFAULT_POINT 对应小数点 本程序统一用扩大到小数点后3位的值，如定值是 10V 返回 10000 ,是1.11V 返回 1110, s 返回的是 ms
int getSetByDescModbus(char* desc)
{
	int i;
	char *modName, *sigName, *dot;
	int modNameLen;

	if (!desc)
		return 0;

	//得到字符串里 . 的位置
	dot = strchr(desc,'.');
	if (!dot)
	{	//没有 .
		sigName = desc;
		modName = 0;
	}
	else
	{	//有 .
		sigName = dot + 1;
		modName = desc;
		modNameLen = dot - modName;
	}

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (modName)		//对模块名筛选
		{
			if (!g_setDataSheet[i].name.modName)
				continue;

			if (strlen(g_setDataSheet[i].name.modName) != modNameLen)
				continue;

			if (strncmp(modName, g_setDataSheet[i].name.modName, modNameLen) != 0)
				continue;
		}
		else
		{
			if (g_setDataSheet[i].name.modName)
				continue;
		}

		if (!strcmp(sigName, g_setDataSheet[i].name.desc))	//比较信号名
		{
			return g_setDataSheet[i].val;
		}
	}
	return 0;
}

int getSetIdByDesc(char* desc)
{

	int i;
	char *modName, *sigName, *dot;
	int modNameLen;

	if (!desc)
		return 0;

	//得到字符串里 . 的位置
	dot = strchr(desc, '.');
	if (!dot)
	{	//没有 .
		sigName = desc;
		modName = 0;
	}
	else
	{	//有 .
		sigName = dot + 1;
		modName = desc;
		modNameLen = dot - modName;
	}

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (modName)		//对模块名筛选
		{
			if (!g_setDataSheet[i].name.modName)
				continue;

			if (strlen(g_setDataSheet[i].name.modName) != modNameLen)
				continue;

			if (strncmp(modName, g_setDataSheet[i].name.modName, modNameLen) != 0)
				continue;
		}
		else
		{
			if (g_setDataSheet[i].name.modName)
				continue;
		}

		if (!strcmp(sigName, g_setDataSheet[i].name.desc))	//比较信号名
		{
			return i+1;
		}
	}
	return 0;
}

//获取定值值，会缩放到 DEFAULT_POINT 对应小数点 本程序统一用扩大到小数点后3位的值，如定值是 10V 返回 10000 ,是1.11V 返回 1110, s 返回的是 ms
int getSetById(unsigned int id)
{
	if (id == 0 || id > getSetDataShtLen())
		return 0;

	return retSetVal(g_setDataSheet[id - 1].val, g_setDataSheet[id - 1].diffPoint);
}

int getSetByIdModbus(unsigned int id)
{
	if (id == 0 || id > getSetDataShtLen())
		return 0;

	return g_setDataSheet[id - 1].val;
}

int getSetPonitById(unsigned int id)
{
	if (id == 0 || id > getSetDataShtLen())
		return 0;

	return g_setDataSheet[id - 1].point;
}

int setLimitById(unsigned int id, int val)
{
	char j;
	int max, min, baseSet;
	
	if (g_setDataSheet[id].type != e_desc && g_setDataSheet[id].type != e_ip)
	{
		//非描述定值 ip 需要 判断最大最小
		max = g_setDataSheet[id].max;
		min	= g_setDataSheet[id].min;		
		//如果界限依赖其他定值,且必须是数值型定值
		if(g_setDataSheet[id].baseSetId && g_setDataSheet[id].type == e_dig)	
		{
			baseSet = getSetById(g_setDataSheet[id].baseSetId);	
			max = max * baseSet / BASESET_COEEF;
			min = min * baseSet / BASESET_COEEF;
			j = DEFAULT_POINT;						//当前小数位数
			while (j != g_setDataSheet[id].point)	//缩放到需要的数值
			{
				if (j < g_setDataSheet[id].point)
				{
					max *= 10;
					min *= 10;
					j++;
				}
				else
				{
					max /= 10;
					min /= 10;
					j--;
				}
			}
		}
		//界限判断
		if (val > max)
			val = max;
		else if (val < min)
			val = min;
	}
	
	return val;
}

void setSetById(unsigned int id, int val)
{

	if (id == 0 || id > getSetDataShtLen())
		return;

	g_setDataSheet[id - 1].val = setLimitById(id - 1, val);
}

s_showName* getSetName(unsigned int id)
{
	if (id == 0 || id > getSetDataShtLen())
		return 0;

	return &g_setDataSheet[id - 1].name.showName;
}

/*更新定值名字*/
void refreshSetName(void)
{
	int i;

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		getName(&g_setDataSheet[i].name, g_langueSel);
	}
}

int* getSetAddr(char* desc)
{
	int i;

	if (!desc)
		return 0;

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (!strcmp(desc, g_setDataSheet[i].name.desc))
		{
			return &g_setDataSheet[i].val;
		}
	}
	return 0;
}

unsigned char getSetplcaddr(char* desc)
{
	int i;

	if (!desc)
		return 0;

	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (!strcmp(desc, g_setDataSheet[i].name.desc))
		{
			return g_setDataSheet[i].plc_addr;
		}
	}
	return 0;
}

//添加定值属性，根据描述符动态配置定值参数
char addSetByDesc(char* desc, int min, int max, int defaultValue, char point, char* unit, unsigned char plc_addr, char* showName)
{
	int i;

	if (!desc)
		return FAIL;

	// 查找对应的定值项
	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (!strcmp(desc, g_setDataSheet[i].name.desc))
		{
			// 设置定值属性
			g_setDataSheet[i].min = min;
			g_setDataSheet[i].max = max;
			g_setDataSheet[i].defaultValue = defaultValue;
			g_setDataSheet[i].point = point;

			// 设置单位（如果提供）
			if (unit)
			{
				g_setDataSheet[i].unit = unit;
			}

			// 设置PLC地址
			g_setDataSheet[i].plc_addr = plc_addr;

			// 设置显示名称（如果提供）
			if (showName)
			{
                g_setDataSheet[i].name.showName.name_string = showName;
				g_setDataSheet[i].name.showName.name_len = strlen(showName);
#if 0
				//!!! 分配内存并复制字符串内容（不能将传入进来的参数直接赋值给全局变量的指针，因为有可能传入的是一个局部变量的指针）
				int len = strlen(showName);
				g_setDataSheet[i].name.showName.name_string = (char*)malloc(len + 1);
				if (g_setDataSheet[i].name.showName.name_string)
				{
					strcpy(g_setDataSheet[i].name.showName.name_string, showName);
					g_setDataSheet[i].name.showName.name_len = len;
				}
				else
				{
					g_setDataSheet[i].name.showName.name_len = 0;
				}
#endif
			}

			// 计算diffPoint（缩放系数）
			if (g_setDataSheet[i].type == e_dig)
			{
				g_setDataSheet[i].diffPoint = DEFAULT_POINT - point;

				// 根据单位调整缩放系数
				if (unit)
				{
					if (!strcmp("s", unit) || !strcmp("S", unit))	// 时间单位
					{
						g_setDataSheet[i].diffPoint = 3 - point;
					}
					else if (!strcmp("KV", unit) || !strcmp("Kv", unit) ||
							 !strcmp("kv", unit) || !strcmp("kV", unit))	// 电压单位
					{
						g_setDataSheet[i].diffPoint += 3;
					}
				}
			}

			// 验证并设置当前值
			if (g_setDataSheet[i].type != e_desc && g_setDataSheet[i].type != e_ip)
			{
				// 确保默认值在有效范围内
				if (defaultValue > max)
				{
					g_setDataSheet[i].val = max;
				}
				else if (defaultValue < min)
				{
					g_setDataSheet[i].val = min;
				}
				else
				{
					g_setDataSheet[i].val = defaultValue;
				}
			}
			else
			{
				g_setDataSheet[i].val = defaultValue;
			}

			return SUCC;
		}
	}

	return FAIL;	// 未找到对应的定值项
}

//设置定值值，根据描述符设置具体变量的值
char setSetByDesc(char* desc, int value)
{
	int i;
	char *modName, *sigName, *dot;
	int modNameLen;

	if (!desc)
		return FAIL;//???

	//得到字符串里 . 的位置
	dot = strchr(desc, '.');
	if (!dot)
	{	//没有 .
		sigName = desc;
		modName = 0;
	}
	else
	{	//有 .
		sigName = dot + 1;
		modName = desc;
		modNameLen = dot - modName;
	}

	// 查找对应的定值项
	for (i = 0; i < getSetDataShtLen(); i++)
	{
		if (!g_setDataSheet[i].name.desc)
			continue;

		if (modName)		//对模块名筛选
		{
			if (!g_setDataSheet[i].name.modName)
				continue;

			if (strlen(g_setDataSheet[i].name.modName) != modNameLen)
				continue;

			if (strncmp(modName, g_setDataSheet[i].name.modName, modNameLen) != 0)
				continue;
		}
		else
		{
			if (g_setDataSheet[i].name.modName)
				continue;
		}

		if (!strcmp(sigName, g_setDataSheet[i].name.desc))	//比较信号名
		{
			// 设置定值，进行范围限制
			if (g_setDataSheet[i].type != e_desc && g_setDataSheet[i].type != e_ip)
			{
				// 确保值在有效范围内
				if (value > g_setDataSheet[i].max)
				{
					g_setDataSheet[i].val = g_setDataSheet[i].max;
				}
				else if (value < g_setDataSheet[i].min)
				{
					g_setDataSheet[i].val = g_setDataSheet[i].min;
				}
				else
				{
					g_setDataSheet[i].val = value;
				}
			}
			else
			{
				g_setDataSheet[i].val = value;
			}

			return SUCC;
		}
	}

	return FAIL;	// 未找到对应的定值项
}

/**************************** 与铁电的相关接口 ***************************************************/
//读出某组定值的一个区
char readSetGroup(e_settingGroup grpId,unsigned char sec)
{
	unsigned char 	i;
	unsigned short 	crc;
	unsigned short 	flashAddr;
	char* 			ramAddr;
	unsigned int 		len;
	
	for(i = 0; i < getSetGrpLen(); i++)
	{
			if(g_settingGroup[i].groupType == grpId)
			{
				if(sec > g_settingGroup[i].sec || sec == 0)
					return FAIL;
				
				len = g_settingGroup[i].len;	//一个区的定值长度
				sec--;
				flashAddr = g_settingGroup[i].flashAddr + sec*(len + 2);		//+2是英文考虑到每个区有2个字节的 crc
				ramAddr   = g_settingGroup[i].ramAddr + sec*len;				//每个区的ram缓存地址
				
				#ifndef WIN32
				if(FRAM_Read_Fun(flashAddr, ramAddr, len)== 0)
				{
					//读出crc
					FRAM_Read_Fun(flashAddr+len, (unsigned char*)&crc, 2);
					if(crc == CRC16(ramAddr,len))
					{
						return SUCC;
					}
					else
					{
						return FAIL;
					}
				}
				else
					return FAIL;
				#else
					return SUCC;
				#endif
			}
	}
	return FAIL;
}

//写入某组定值
char writeSetGroup(e_settingGroup grpId,unsigned char sec)
{
	unsigned char 	i;
	unsigned short 	crc;
	unsigned short 	flashAddr;
	char* 					ramAddr;
	unsigned int 		len;
	
	for(i = 0; i < getSetGrpLen(); i++)
	{
			if(g_settingGroup[i].groupType == grpId)
			{
				if(sec > g_settingGroup[i].sec || sec == 0)
					return FAIL;
				
				len = g_settingGroup[i].len;				//一个区的定值长度
				sec--;
				flashAddr = g_settingGroup[i].flashAddr + sec*(len + 2);	//每个区有2个字节的 crc
				ramAddr   = g_settingGroup[i].ramAddr + sec*len;			//每个区的ram缓存地址
				
				#ifndef WIN32
				if(FRAM_Write_Fun(flashAddr, ramAddr, len)== 0)
				{
					crc = CRC16(ramAddr,len);
					FRAM_Write_Fun(flashAddr+len, (unsigned char*)&crc, 2);
					
					if(grpId != inner_setting)
						creatOpEvent(op_setModify);
					return SUCC;
				}
				else
				{
					return FAIL;
				}
				#else
					return SUCC;
				#endif
			}
	}
	return FAIL;
}

/**************************************  定值组相关接口   *************************************/
//返回定值组区数目
unsigned char getSetNoNums(e_settingGroup type)
{
	int i;
	for (i = 0; i < getSetGrpLen(); i++)
	{
		if(g_settingGroup[i].groupType == type)
			return g_settingGroup[i].sec;
	}
	return 1;
}

//从缓冲区更新定值到定值数据集里
void assignSetDataSheet(e_settingGroup Group, unsigned char modInit)
{
	int i,j;
	s_setRefTabItem* refTab;
	int dataLen;
	s_settingGroup* setGroup = g_settingGroup;
	char sector;

	deviceBs(SETTING_CHAGE);		//闭锁
	if (Group == protect_setting)
		sector = getSetByDesc("Settig_SecNo");	//待优化
	else
		sector = 1;
		
	//遍历定值组
	for (i = 0; i < getSetGrpLen(); i++)
	{ 
		if (Group == setGroup->groupType)
		{
			if (sector > setGroup->sec || sector <= 0)
				break;

			//遍历定值表
			sector--;
			refTab = setGroup->setRefTabl;
			dataLen = setGroup->len;
			for (j = 0; j < setGroup->indexNum; j++, refTab++)
			{
				if (!refTab->idx)
					continue;
				//赋值到数据集
				if (Group == soft_setting)
				{
					setSetById(refTab->idx, (int)(*(setGroup->ramAddr + sector*dataLen + j)));
				}
				else if (Group == desc_setting)
				{
					setSetById(refTab->idx, (int)(setGroup->ramAddr + sector*dataLen + j*DES_SET_LEN));
				}
				else
				{
					setSetById(refTab->idx, *(int *)(setGroup->ramAddr + sector*dataLen + j*(sizeof(int))));
				}
			}
			break;
		}
		setGroup++;
	}
	
	if(modInit)
		initModParm();					//更新模块定值
	deviceClearBs(SETTING_CHAGE);	//解除闭锁
}

//获取定值组缓冲区定值信息(不用放大缩小，直接取原始信息,用于通信，界面显示用)
char getSetInfo(unsigned int id, e_settingGroup Group, unsigned char sec, s_103SetInfo* setInfo)
{
	int i, j;
	s_setRefTabItem* refTab;
	int dataLen;
	s_settingGroup* setGroup = g_settingGroup;

	if (id == 0 || id > getSetDataShtLen())
		return FAIL;

	setInfo->type = g_setDataSheet[id - 1].type;
	setInfo->point = g_setDataSheet[id - 1].point;
	setInfo->unit = g_setDataSheet[id - 1].unit;
	//setInfo->val = g_setDataSheet[id - 1].val;

	//遍历定值组
	for (i = 0; i < getSetGrpLen(); i++)
	{
		if (Group == setGroup->groupType)
		{
			if (sec > setGroup->sec || sec <= 0)
				sec = 1;

			//遍历定值表
			sec--;
			refTab = setGroup->setRefTabl;
			dataLen = setGroup->len;
			for (j = 0; j < setGroup->indexNum; j++, refTab++)
			{
				if (!refTab->idx)
					continue;
				if (refTab->idx != id)
					continue;

				//赋值到数据集
				if (Group == soft_setting)
				{
					setInfo->val = (int)(*(setGroup->ramAddr + sec*dataLen + j));
				}
				else if (Group == desc_setting)
				{
					setInfo->val = (int)(setGroup->ramAddr + sec*dataLen + j*DES_SET_LEN);
				}
				else
				{
					setInfo->val = *(int *)(setGroup->ramAddr + sec*dataLen + j*(sizeof(int)));
				}
				return SUCC;
			}
			break;
		}
		setGroup++;
	}

	return FAIL;
}

//修改定值组缓冲区数值 单个定值
void writeSetToBuf(e_settingGroup Group, char sector, int id, int val)
{
	int i, j;
	s_setRefTabItem* refTab;
	int dataLen;
	s_settingGroup* setGroup = g_settingGroup;

	if (!id)
		return;

	//遍历定值组
	for (i = 0; i < getSetGrpLen(); i++)
	{
		if (Group == setGroup->groupType)
		{
			if (sector > setGroup->sec || sector <= 0)
				return;

			//遍历定值表
			sector--;
			refTab = setGroup->setRefTabl;
			dataLen = setGroup->len;
			for (j = 0; j < setGroup->indexNum; j++)
			{
				if (id == refTab->idx)
				{
					id--;
					if (Group == soft_setting)
					{
						val = setLimitById(id , val);
						(*(setGroup->ramAddr + sector*dataLen + j)) = (char)val;
					}
					else if (Group == desc_setting)
					{
							memcpy(setGroup->ramAddr + sector*dataLen + j*DES_SET_LEN, (const void*)val, DES_SET_LEN);
					}
					else
					{
						if(g_setDataSheet[id].type != e_ip)
						{
								val = setLimitById(id , val);			
						}
						*(int *)(setGroup->ramAddr + sector*dataLen + j*(sizeof(int))) = val;
					}
					
					return;
				}
				refTab++;
			}
			return;
		}
		setGroup++;
	}
}

//设置定值缓存区数据为默认值
void setDefaultValToBuf(char Group, char sector)
{
	int i;
	int id, val;
	int dataLen;
	char *ramAddr;
	char *descStr;
	unsigned char descLen;

	if(sector == 0)
		return;
	
	sector--;
	
	dataLen = g_settingGroup[Group].len;
	ramAddr = g_settingGroup[Group].ramAddr;

	for (i = 0; i < g_settingGroup[Group].indexNum; i++)
	{
		id = g_settingGroup[Group].setRefTabl[i].idx;
		if (id == 0)
			continue;
		id--;
		val = g_setDataSheet[id].val;
		if (Group == soft_setting)
		{
			(*(ramAddr + sector*dataLen + i)) = (char)val;
		}
		else if (Group == desc_setting)
		{
			if(val)
			{
				descLen = strlen((const char*)val);
				if(descLen > (DES_SET_LEN - 1))
					descLen =  (DES_SET_LEN - 1);
				descStr = ramAddr + sector*dataLen + i*DES_SET_LEN;
				memset(descStr, 0, DES_SET_LEN);
				memcpy(descStr, (const void*)val, descLen);
			}
		}
		else
		{
			if(g_setDataSheet[id].type != e_ip)
			{
					val = setLimitById(id , val);
			}
			*(int *)(ramAddr + sector*dataLen + i*(sizeof(int))) = val;
		}

	}
}


/**************************** 初始化相关接口 ***************************************************/
//初始化引用表
void initSetTable(void)
{
	unsigned short i, j, k;
	int in;

	//初始化定值数据集
	for (i = 0; i < getSetDataShtLen(); i++)
	{
		//对定值最大最小值的特殊处理
		if (g_setDataSheet[i].type == e_bool)
		{
			g_setDataSheet[i].max = 1;
			g_setDataSheet[i].min = 0;
			g_setDataSheet[i].point = 0;
		}

		//获取baseSetId
		if(g_setDataSheet[i].baseSet)
			g_setDataSheet[i].baseSetId =  getSetIdByDesc(g_setDataSheet[i].baseSet);

		//定值先赋值默认值 或者最大最小值
		if (g_setDataSheet[i].type != e_desc && g_setDataSheet[i].type != e_ip)
		{
			if (g_setDataSheet[i].defaultValue > g_setDataSheet[i].max)
			{
				g_setDataSheet[i].val = g_setDataSheet[i].max;
			}
			else if (g_setDataSheet[i].defaultValue < g_setDataSheet[i].min)
			{
				g_setDataSheet[i].val = g_setDataSheet[i].min;
			}
			else
			{
				g_setDataSheet[i].val = g_setDataSheet[i].defaultValue;
			}
		}
		else
		{
			g_setDataSheet[i].val = g_setDataSheet[i].defaultValue;
		}

		if (g_setDataSheet[i].type == e_dig)// || g_setDataSheet[i].type == e_digIn)
		{
			//根据单位和小数点计算定值实际使用需要的缩放倍数,时间秒固定3位小数,因为内部采用ms累加计时
			g_setDataSheet[i].diffPoint = DEFAULT_POINT - g_setDataSheet[i].point;
			if (!g_setDataSheet[i].unit)
				continue;
			if (!strcmp("s", g_setDataSheet[i].unit) || !strcmp("S", g_setDataSheet[i].unit))	//时间
			{
				g_setDataSheet[i].diffPoint = 3 - g_setDataSheet[i].point;
			}
			else if (!strcmp("KV", g_setDataSheet[i].unit) || !strcmp("Kv", g_setDataSheet[i].unit) || !strcmp("kv", g_setDataSheet[i].unit) || !strcmp("kV", g_setDataSheet[i].unit)) //电压
			{
				g_setDataSheet[i].diffPoint += 3;
			}
		}
	}

	//遍历定值组
	for (i = 0; i < getSetGrpLen(); i++)
	{
		//分配铁电空间 (每区+2是因为要留空间存CRC)
		g_settingGroup[i].flashAddr =  allocFlashSpace((g_settingGroup[i].len + 2)*g_settingGroup[i].sec);

		//遍历定值表 填id
		for (j = 0; j < g_settingGroup[i].indexNum; j++)
		{
			if (!g_settingGroup[i].setRefTabl[j].desc)
				continue;
			//从数据集里匹配到对应定值
			for (k = 0; k < getSetDataShtLen(); k++)
			{
				if (!g_setDataSheet[k].name.desc)
					continue;		
				g_settingGroup[i].setRefTabl[j].idx = getSetIdByDesc(g_settingGroup[i].setRefTabl[j].desc);
			}
		}
	}
}

//定值初始化任务
char initSet(void)
{
	unsigned char i,sec,errFlg = 0;
	
	//初始化表	全部赋值默认值
	initSetTable();
	
	//从铁电读取定值到 缓存区 
	for(i = 0; i < getSetGrpLen(); i++)
	{
		for(sec = 1; sec <= g_settingGroup[i].sec; sec++)
		{
			if(readSetGroup(g_settingGroup[i].groupType,	sec) == FAIL)
			{
				//缓存区数据不对，全部赋默认定值
				setDefaultValToBuf(i,	sec);
				//如果是系统定值、保护定值、软压板、系数定值 不对则置闭锁标志
				if(sys_setting == g_settingGroup[i].groupType || protect_setting == g_settingGroup[i].groupType || soft_setting == g_settingGroup[i].groupType || Mcoeff_setting == g_settingGroup[i].groupType || Pcoeff_setting == g_settingGroup[i].groupType)
					errFlg = 0;//TODO，可能因为定值暂时不能设置，暂时屏蔽这个功能
			}
		}
		assignSetDataSheet(g_settingGroup[i].groupType, 0);
	}
	
	//从 缓存区 赋值到 定值数据集(运行使用区)
	for(i = 0; i < getSetGrpLen(); i++)
	{
		assignSetDataSheet(g_settingGroup[i].groupType, 0);	
	}

	//更新数据集里定值名字
	refreshSetName();

	//判断定值是否错误
	if(errFlg)
	{
		deviceBs(SETTING_ERR);		//设置定值闭锁
		//creatOpEvent(op_settingErr);	//上报SOE
	}
	
	return SUCC;
}
